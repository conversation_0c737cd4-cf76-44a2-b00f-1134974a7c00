# English Assessment API Documentation

## Overview
This document provides comprehensive API documentation for the enhanced English assessment system, including server endpoints, data flow, and integration patterns.

## Server Endpoints

### 1. English Proficiency Analysis Endpoint
**Endpoint**: `POST /api/analyze-english-proficiency`

**Purpose**: Analyzes student English responses using AI and returns detailed feedback

**Request Format**:
```javascript
{
  "response": "Student's written response text...",
  "email": "<EMAIL>",
  "userType": "student",
  "studentLevel": "adult-learner"
}
```

**Response Format**:
```javascript
{
  "success": true,
  "analysis": {
    "score": 18,
    "level": "L2/GCSE",
    "feedback": {
      "grammar": "Excellent grammatical accuracy with complex sentence structures...",
      "vocabulary": "Rich and varied vocabulary with appropriate academic language...",
      "coherence": "Ideas flow logically with excellent use of transitional phrases...",
      "overall": "Outstanding L2/GCSE level performance. Ready for advanced academic work..."
    },
    "strengths": [
      "Sophisticated sentence structures",
      "Rich descriptive vocabulary",
      "Excellent organization and flow",
      "Clear argumentation"
    ],
    "improvements": [
      "Refine conditional clause usage",
      "Expand formal academic vocabulary",
      "Practice advanced punctuation"
    ]
  }
}
```

**Error Response**:
```javascript
{
  "success": false,
  "error": "Error message describing the issue",
  "fallback": {
    "score": 0,
    "level": "Entry",
    "feedback": {
      "grammar": "Assessment completed",
      "vocabulary": "Vocabulary evaluated",
      "coherence": "Structure assessed",
      "overall": "Assessment completed successfully"
    },
    "strengths": ["Completed the assessment"],
    "improvements": ["Continue practicing English"]
  }
}
```

**Implementation Details**:
```javascript
// Server-side implementation (server.js)
app.post('/api/analyze-english-proficiency', async (req, res) => {
  try {
    const { response, email, userType, studentLevel } = req.body;
    
    // Validate input
    if (!response || !email) {
      return res.status(400).json({
        success: false,
        error: 'Response and email are required'
      });
    }
    
    // AI Analysis (using OpenAI or similar)
    const analysis = await analyzeEnglishProficiency(response);
    
    res.json({
      success: true,
      analysis: analysis
    });
    
  } catch (error) {
    console.error('English proficiency analysis error:', error);
    res.status(500).json({
      success: false,
      error: 'Analysis failed',
      fallback: getDefaultAnalysis()
    });
  }
});
```

## Data Flow Architecture

### 1. Assessment Submission Flow
```
Student Submits Response
        ↓
Frontend Validation
        ↓
POST /api/analyze-english-proficiency
        ↓
AI Analysis Processing
        ↓
Enhanced Feedback Generation
        ↓
Response with Detailed Analysis
        ↓
Frontend Stores to Firestore
        ↓
UI Updates with Results
```

### 2. Retake Detection Flow
```
Form Submission
        ↓
Check Existing Assessment Data
        ↓
If Score < 16: Show Assessment with Retake Message
        ↓
If Score ≥ 16: Proceed to Digital Skills
        ↓
If No Assessment: Show Fresh Assessment
```

### 3. Database Update Flow
```javascript
// Frontend database update after AI analysis
async function storeEnglishAssessmentResults(email, response, analysisResult) {
  const updateData = {
    // Core fields
    englishProficiencyScore: analysisResult.score,
    englishProficiencyLevel: analysisResult.level,
    englishAssessmentCompleted: true,
    englishResponse: response,
    englishAssessmentTimestamp: firebase.firestore.FieldValue.serverTimestamp(),
    timeSpentOnEnglish: this.timeLimit - this.timeRemaining,
    
    // Enhanced fields
    englishFeedback: analysisResult.feedback || getDefaultFeedback(),
    englishStrengths: analysisResult.strengths || getDefaultStrengths(),
    englishImprovements: analysisResult.improvements || getDefaultImprovements(),
    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
  };
  
  await userRef.update(updateData);
}
```

## AI Analysis Integration

### 1. OpenAI Integration Pattern
```javascript
async function analyzeEnglishProficiency(response) {
  const prompt = `
    Analyze this English writing sample for L2/GCSE level assessment.
    Provide detailed feedback on grammar, vocabulary, and coherence.
    Score from 0-21 where 16+ indicates L2/GCSE level proficiency.
    
    Writing sample: "${response}"
    
    Respond with JSON format:
    {
      "score": number,
      "level": "Entry|L1|L2/GCSE",
      "feedback": {
        "grammar": "detailed grammar analysis",
        "vocabulary": "vocabulary assessment", 
        "coherence": "organization and clarity evaluation",
        "overall": "comprehensive summary"
      },
      "strengths": ["strength1", "strength2"],
      "improvements": ["improvement1", "improvement2"]
    }
  `;
  
  const completion = await openai.chat.completions.create({
    model: "gpt-4",
    messages: [{ role: "user", content: prompt }],
    temperature: 0.3,
    max_tokens: 1000
  });
  
  return JSON.parse(completion.choices[0].message.content);
}
```

### 2. Fallback Analysis System
```javascript
function getDefaultAnalysis() {
  return {
    score: 0,
    level: "Entry",
    feedback: {
      grammar: "Assessment completed",
      vocabulary: "Vocabulary evaluated",
      coherence: "Structure assessed", 
      overall: "Assessment completed successfully"
    },
    strengths: ["Completed the assessment"],
    improvements: ["Continue practicing English"]
  };
}
```

## Frontend Integration Patterns

### 1. Assessment Submission Handler
```javascript
class EnglishAssessment {
  async submitAssessment() {
    try {
      const response = document.getElementById('english-response').value;
      const email = document.getElementById('email').value;
      
      // Show loading state
      this.showLoadingState();
      
      // Call AI analysis API
      const analysisResponse = await fetch('/api/analyze-english-proficiency', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          response: response,
          email: email,
          userType: 'student',
          studentLevel: document.getElementById('student-level').value
        })
      });
      
      const result = await analysisResponse.json();
      
      if (result.success) {
        // Store results to database
        await this.storeEnglishAssessmentResults(email, response, result.analysis);
        
        // Show results UI
        this.showResults(result.analysis);
      } else {
        // Handle API error with fallback
        await this.storeEnglishAssessmentResults(email, response, result.fallback);
        this.showResults(result.fallback);
      }
      
    } catch (error) {
      console.error('Assessment submission error:', error);
      this.showErrorState(error);
    }
  }
}
```

### 2. Real-time Progress Tracking
```javascript
// Track assessment progress and time spent
class AssessmentTracker {
  constructor() {
    this.startTime = Date.now();
    this.keystrokes = 0;
    this.focusTime = 0;
  }
  
  trackProgress() {
    // Track typing activity
    document.getElementById('english-response').addEventListener('input', () => {
      this.keystrokes++;
    });
    
    // Track focus time
    document.getElementById('english-response').addEventListener('focus', () => {
      this.focusStartTime = Date.now();
    });
    
    document.getElementById('english-response').addEventListener('blur', () => {
      if (this.focusStartTime) {
        this.focusTime += Date.now() - this.focusStartTime;
      }
    });
  }
  
  getMetrics() {
    return {
      totalTime: Date.now() - this.startTime,
      focusTime: this.focusTime,
      keystrokes: this.keystrokes,
      wordsWritten: document.getElementById('english-response').value.split(' ').length
    };
  }
}
```

## Error Handling Strategies

### 1. API Error Handling
```javascript
async function robustApiCall(endpoint, data) {
  const maxRetries = 3;
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
        timeout: 30000 // 30 second timeout
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
      
    } catch (error) {
      lastError = error;
      console.warn(`API call attempt ${attempt} failed:`, error.message);
      
      if (attempt < maxRetries) {
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }
  
  // All retries failed, return fallback
  console.error('All API retry attempts failed:', lastError);
  return {
    success: false,
    error: lastError.message,
    fallback: getDefaultAnalysis()
  };
}
```

### 2. Database Error Recovery
```javascript
async function safelyStoreAssessmentData(email, data) {
  try {
    await storeEnglishAssessmentResults(email, data);
    return { success: true };
    
  } catch (error) {
    console.error('Database storage failed:', error);
    
    // Try to store minimal data
    try {
      await storeMinimalAssessmentData(email, data);
      return { success: true, warning: 'Stored with reduced data' };
      
    } catch (fallbackError) {
      console.error('Fallback storage also failed:', fallbackError);
      return { success: false, error: fallbackError.message };
    }
  }
}
```

## Performance Optimization

### 1. Response Caching
```javascript
// Cache AI analysis responses to avoid duplicate API calls
class AnalysisCache {
  constructor() {
    this.cache = new Map();
    this.maxSize = 100;
  }
  
  generateKey(response, userLevel) {
    return btoa(response + userLevel).substring(0, 32);
  }
  
  get(response, userLevel) {
    const key = this.generateKey(response, userLevel);
    return this.cache.get(key);
  }
  
  set(response, userLevel, analysis) {
    const key = this.generateKey(response, userLevel);
    
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      analysis,
      timestamp: Date.now()
    });
  }
}
```

### 2. Batch Processing
```javascript
// For administrative dashboards - batch fetch multiple assessments
async function fetchMultipleAssessments(emails, companyId) {
  const batchSize = 10;
  const results = [];
  
  for (let i = 0; i < emails.length; i += batchSize) {
    const batch = emails.slice(i, i + batchSize);
    const batchPromises = batch.map(email => 
      fetchEnglishAssessmentData(email, companyId).catch(error => ({
        email,
        error: error.message
      }))
    );
    
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
  }
  
  return results;
}
```

## Security Considerations

### 1. Input Validation
```javascript
function validateAssessmentInput(data) {
  const { response, email, userType, studentLevel } = data;
  
  // Validate required fields
  if (!response || typeof response !== 'string') {
    throw new Error('Valid response text is required');
  }
  
  if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    throw new Error('Valid email address is required');
  }
  
  // Validate response length
  if (response.length < 50) {
    throw new Error('Response must be at least 50 characters');
  }
  
  if (response.length > 2000) {
    throw new Error('Response must not exceed 2000 characters');
  }
  
  // Sanitize input
  return {
    response: response.trim(),
    email: email.toLowerCase().trim(),
    userType: userType || 'student',
    studentLevel: studentLevel || 'adult-learner'
  };
}
```

### 2. Rate Limiting
```javascript
// Server-side rate limiting for assessment submissions
const rateLimit = require('express-rate-limit');

const assessmentRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Maximum 5 assessment submissions per 15 minutes
  message: {
    success: false,
    error: 'Too many assessment attempts. Please wait before trying again.'
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.post('/api/analyze-english-proficiency', assessmentRateLimit, async (req, res) => {
  // Assessment logic here
});
```

## Monitoring and Analytics

### 1. Assessment Metrics Tracking
```javascript
// Track assessment completion metrics
function trackAssessmentMetrics(email, analysisResult, timeSpent) {
  const metrics = {
    timestamp: new Date().toISOString(),
    email: email,
    score: analysisResult.score,
    level: analysisResult.level,
    timeSpent: timeSpent,
    wordCount: analysisResult.wordCount || 0,
    retakeAttempt: analysisResult.isRetake || false
  };
  
  // Send to analytics service
  if (typeof gtag !== 'undefined') {
    gtag('event', 'english_assessment_completed', {
      custom_parameter_score: metrics.score,
      custom_parameter_level: metrics.level,
      custom_parameter_time_spent: metrics.timeSpent
    });
  }
  
  console.log('Assessment metrics:', metrics);
}
```

This comprehensive API documentation provides developers with all the necessary information to integrate with and extend the enhanced English assessment system.
